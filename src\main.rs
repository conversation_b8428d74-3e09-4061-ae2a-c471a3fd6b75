//! **overlay_app.rs** – Zero‑chrome overlay that spawns free‑floating egui windows (egui 0.31.1 / eframe 0.31.1)
//! -------------------------------------------------------------------------------------------------------------
//! Goals
//! -----
//! * No visible “main” window – we hide a 1×1 transparent root viewport in the top‑left corner.
//! * Pop‑up windows are real OS *viewports* (not internal egui::Window widgets) so users can drag them anywhere on‑screen,
//!   even outside the root viewport.
//! * Hot‑key driven (1/2/3 = Windows, Q = Quit) for demo purposes – replace with a global hot‑key, system‑tray, etc.
//! * All viewports stay **always‑on‑top**.
//!
//! Usage
//! ------
//! ```bash
//! cargo new overlay_app && cd overlay_app
//! # ⬇ paste this file as src/main.rs and add the Cargo.toml snippet
//! cargo run --release
//! ```
//! Cargo.toml:
//! ```toml
//! [dependencies]
//! egui   = "0.31.1"
//! eframe = { version = "0.31.1", default-features = false, features = ["wgpu"] }
//! ```
//!
//! Limitations
//! -----------
//! * Hot‑keys are caught only while *any* viewport has focus. For true global hot‑keys, wrap rdev/shortcut‑hook/etc.
//! * macOS/Linux: Transparent click‑through may need a platform‑specific compositor flag.
//!
// =========================================================================================
use eframe::{egui, egui::Key, App, CreationContext};
use egui::ViewportBuilder;

struct OverlayApp {
    show_window1: bool,
    show_window2: bool,
    show_window3: bool,
}

impl OverlayApp {
    fn new(_cc: &CreationContext<'_>) -> Self {
        Self { 
            show_window1: false, 
            show_window2: false, 
            show_window3: false 
        }
    }


}

impl App for OverlayApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // Clear the background to be transparent
        ctx.set_visuals(egui::Visuals {
            window_fill: egui::Color32::TRANSPARENT,
            panel_fill: egui::Color32::TRANSPARENT,
            ..ctx.style().visuals.clone()
        });
        
        // ── Hot‑keys ───────────────────────────────────────────────────────────
        if ctx.input(|i| i.key_pressed(Key::Num1)) {
            self.show_window1 = !self.show_window1;
        }
        if ctx.input(|i| i.key_pressed(Key::Num2)) {
            self.show_window2 = !self.show_window2;
        }
        if ctx.input(|i| i.key_pressed(Key::Num3)) {
            self.show_window3 = !self.show_window3;
        }
        if ctx.input(|i| i.key_pressed(Key::Q)) {
            ctx.send_viewport_cmd(egui::ViewportCommand::Close);
        }

        // Show floating windows directly in the overlay
        if self.show_window1 {
            egui::Window::new("🎯 Window One")
                .title_bar(false)
                .resizable(false)
                .collapsible(false)
                .default_pos([100.0, 100.0])
                .fixed_size([320.0, 220.0])
                .frame(
                    egui::Frame::new()
                        .fill(egui::Color32::from_rgba_premultiplied(30, 30, 40, 200))
                        .corner_radius(12.0)
                        .inner_margin(20.0)
                )
                .show(ctx, |ui| {
                    // Beautiful title
                    ui.label(
                        egui::RichText::new("🎯 Window One")
                            .size(24.0)
                            .color(egui::Color32::from_rgb(120, 180, 255))
                            .strong()
                    );

                    ui.add_space(8.0);

                    // Subtitle
                    ui.label(
                        egui::RichText::new("Primary Control Panel")
                            .size(14.0)
                            .color(egui::Color32::from_rgb(160, 160, 180))
                            .italics()
                    );

                    ui.add_space(15.0);

                    // Content
                    ui.label(
                        egui::RichText::new("This is your main floating workspace")
                            .size(16.0)
                            .color(egui::Color32::from_rgb(220, 220, 235))
                    );

                    ui.add_space(20.0);

                    // Styled close button
                    if ui.add_sized(
                        [120.0, 35.0],
                        egui::Button::new(
                            egui::RichText::new("✕ Close")
                                .size(14.0)
                                .color(egui::Color32::WHITE)
                        )
                        .fill(egui::Color32::from_rgb(220, 80, 80))
                        .corner_radius(8.0)
                    ).clicked() {
                        self.show_window1 = false;
                    }
                });
        }

        if self.show_window2 {
            egui::Window::new("⚡ Window Two")
                .title_bar(false)
                .resizable(false)
                .collapsible(false)
                .default_pos([450.0, 150.0])
                .fixed_size([370.0, 280.0])
                .frame(
                    egui::Frame::new()
                        .fill(egui::Color32::from_rgba_premultiplied(25, 40, 30, 180))
                        .corner_radius(16.0)
                        .inner_margin(22.0)
                )
                .show(ctx, |ui| {
                    ui.add_space(8.0);

                    // Green-themed title
                    ui.label(
                        egui::RichText::new("⚡ Window Two")
                            .size(26.0)
                            .color(egui::Color32::from_rgb(120, 255, 150))
                            .strong()
                    );

                    ui.add_space(10.0);

                    // Subtitle
                    ui.label(
                        egui::RichText::new("Tools & Configuration")
                            .size(15.0)
                            .color(egui::Color32::from_rgb(150, 200, 160))
                            .italics()
                    );

                    ui.add_space(18.0);

                    // Content with multiple lines
                    ui.label(
                        egui::RichText::new("Advanced settings and controls")
                            .size(16.0)
                            .color(egui::Color32::from_rgb(210, 230, 215))
                    );

                    ui.add_space(8.0);

                    ui.label(
                        egui::RichText::new("Configure your overlay experience")
                            .size(14.0)
                            .color(egui::Color32::from_rgb(180, 200, 185))
                    );

                    ui.add_space(25.0);

                    // Styled close button with green theme
                    if ui.add_sized(
                        [130.0, 38.0],
                        egui::Button::new(
                            egui::RichText::new("✕ Close")
                                .size(15.0)
                                .color(egui::Color32::WHITE)
                        )
                        .fill(egui::Color32::from_rgb(180, 100, 100))
                        .corner_radius(10.0)
                    ).clicked() {
                        self.show_window2 = false;
                    }
                });
        }

        if self.show_window3 {
            egui::Window::new("🌟 Window Three")
                .title_bar(false)
                .resizable(false)
                .collapsible(false)
                .default_pos([200.0, 400.0])
                .fixed_size([420.0, 320.0])
                .frame(
                    egui::Frame::new()
                        .fill(egui::Color32::from_rgba_premultiplied(40, 25, 45, 160))
                        .corner_radius(20.0)
                        .inner_margin(25.0)
                )
                .show(ctx, |ui| {
                    ui.add_space(10.0);

                    // Purple-themed title
                    ui.label(
                        egui::RichText::new("🌟 Window Three")
                            .size(28.0)
                            .color(egui::Color32::from_rgb(200, 120, 255))
                            .strong()
                    );

                    ui.add_space(12.0);

                    // Subtitle
                    ui.label(
                        egui::RichText::new("Analytics & Information Hub")
                            .size(16.0)
                            .color(egui::Color32::from_rgb(180, 150, 200))
                            .italics()
                    );

                    ui.add_space(20.0);

                    // Multiple content lines
                    ui.label(
                        egui::RichText::new("Enhanced monitoring and insights")
                            .size(17.0)
                            .color(egui::Color32::from_rgb(220, 200, 235))
                    );

                    ui.add_space(10.0);

                    ui.label(
                        egui::RichText::new("Real-time data visualization")
                            .size(15.0)
                            .color(egui::Color32::from_rgb(190, 170, 210))
                    );

                    ui.add_space(8.0);

                    ui.label(
                        egui::RichText::new("Perfect for dashboards and metrics")
                            .size(14.0)
                            .color(egui::Color32::from_rgb(170, 150, 190))
                    );

                    ui.add_space(30.0);

                    // Styled close button with purple theme
                    if ui.add_sized(
                        [140.0, 40.0],
                        egui::Button::new(
                            egui::RichText::new("✕ Close")
                                .size(16.0)
                                .color(egui::Color32::WHITE)
                        )
                        .fill(egui::Color32::from_rgb(160, 80, 160))
                        .corner_radius(12.0)
                    ).clicked() {
                        self.show_window3 = false;
                    }
                });
        }
    }
}

fn main() -> eframe::Result<()> {
    let options = eframe::NativeOptions {
        viewport: ViewportBuilder::default()
            .with_maximized(true)              // maximized to cover screen
            .with_transparent(true)
            .with_always_on_top()
            .with_decorations(false)
            .with_mouse_passthrough(true),
        hardware_acceleration: eframe::HardwareAcceleration::Required,
        ..Default::default()
    };

    eframe::run_native(
        "overlay_stub",   // root window title (never seen)
        options,
        Box::new(|cc| Ok(Box::new(OverlayApp::new(cc)))),
    )
}






